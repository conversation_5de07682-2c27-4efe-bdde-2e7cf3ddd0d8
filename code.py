import math
import random

def calculate_area(shape, **kwargs):
    """Calculates the area of various shapes.

    Args:
        shape (str): The type of shape ("circle" or "rectangle").
        **kwargs: Additional keyword arguments specific to the shape.
                  For "circle", expects 'radius'.
                  For "rectangle", expects 'width' and 'height'.

    Returns:
        float: The calculated area, or 0 if the shape is not recognized.
    """
    if shape == "circle":
        return math.pi * kwargs["radius"] ** 2
    elif shape == "rectangle":
        return kwargs["width"] * kwargs["height"]
    else:
        return 0

def generate_random_numbers(count=5):
    """Generates a list of random integers.

    Args:
        count (int, optional): The number of random integers to generate. Defaults to 5.

    Returns:
        list: A list of random integers between 1 and 100.
    """
    return [random.randint(1, 100) for _ in range(count)]

class Calculator:
    """A simple calculator class to perform basic arithmetic and store history."""

    def __init__(self):
        """Initializes the Calculator with an empty history list."""
        self.history = []

    def add(self, a, b):
        """Adds two numbers and records the operation in history.

        Args:
            a (int or float): The first number.
            b (int or float): The second number.

        Returns:
            int or float: The sum of a and b.
        """
        result = a + b
        # Record the operation and result in the history
        self.history.append(f"{a} + {b} = {result}")
        return result

    def get_history(self):
        """Retrieves the list of all performed operations.

        Returns:
            list: A list of strings, each representing an operation from history.
        """
        return self.history

calc = Calculator()
result = calc.add(5, 3)
area = calculate_area("circle", radius=5)
numbers = generate_random_numbers(3)
print(f"Results: {result}, {area:.2f}, {numbers}")