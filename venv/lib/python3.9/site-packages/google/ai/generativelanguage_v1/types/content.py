# -*- coding: utf-8 -*-
# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
from __future__ import annotations

from typing import MutableMapping, MutableSequence

import proto  # type: ignore

__protobuf__ = proto.module(
    package="google.ai.generativelanguage.v1",
    manifest={
        "Modality",
        "Content",
        "Part",
        "Blob",
        "ModalityTokenCount",
    },
)


class Modality(proto.Enum):
    r"""Content Part modality

    Values:
        MODALITY_UNSPECIFIED (0):
            Unspecified modality.
        TEXT (1):
            Plain text.
        IMAGE (2):
            Image.
        VIDEO (3):
            Video.
        AUDIO (4):
            Audio.
        DOCUMENT (5):
            Document, e.g. PDF.
    """
    MODALITY_UNSPECIFIED = 0
    TEXT = 1
    IMAGE = 2
    VIDEO = 3
    AUDIO = 4
    DOCUMENT = 5


class Content(proto.Message):
    r"""The base structured datatype containing multi-part content of a
    message.

    A ``Content`` includes a ``role`` field designating the producer of
    the ``Content`` and a ``parts`` field containing multi-part data
    that contains the content of the message turn.

    Attributes:
        parts (MutableSequence[google.ai.generativelanguage_v1.types.Part]):
            Ordered ``Parts`` that constitute a single message. Parts
            may have different MIME types.
        role (str):
            Optional. The producer of the content. Must
            be either 'user' or 'model'.
            Useful to set for multi-turn conversations,
            otherwise can be left blank or unset.
    """

    parts: MutableSequence["Part"] = proto.RepeatedField(
        proto.MESSAGE,
        number=1,
        message="Part",
    )
    role: str = proto.Field(
        proto.STRING,
        number=2,
    )


class Part(proto.Message):
    r"""A datatype containing media that is part of a multi-part ``Content``
    message.

    A ``Part`` consists of data which has an associated datatype. A
    ``Part`` can only contain one of the accepted types in
    ``Part.data``.

    A ``Part`` must have a fixed IANA MIME type identifying the type and
    subtype of the media if the ``inline_data`` field is filled with raw
    bytes.

    This message has `oneof`_ fields (mutually exclusive fields).
    For each oneof, at most one member field can be set at the same time.
    Setting any member of the oneof automatically clears all other
    members.

    .. _oneof: https://proto-plus-python.readthedocs.io/en/stable/fields.html#oneofs-mutually-exclusive-fields

    Attributes:
        text (str):
            Inline text.

            This field is a member of `oneof`_ ``data``.
        inline_data (google.ai.generativelanguage_v1.types.Blob):
            Inline media bytes.

            This field is a member of `oneof`_ ``data``.
    """

    text: str = proto.Field(
        proto.STRING,
        number=2,
        oneof="data",
    )
    inline_data: "Blob" = proto.Field(
        proto.MESSAGE,
        number=3,
        oneof="data",
        message="Blob",
    )


class Blob(proto.Message):
    r"""Raw media bytes.

    Text should not be sent as raw bytes, use the 'text' field.

    Attributes:
        mime_type (str):
            The IANA standard MIME type of the source data. Examples:

            -  image/png
            -  image/jpeg If an unsupported MIME type is provided, an
               error will be returned. For a complete list of supported
               types, see `Supported file
               formats <https://ai.google.dev/gemini-api/docs/prompting_with_media#supported_file_formats>`__.
        data (bytes):
            Raw bytes for media formats.
    """

    mime_type: str = proto.Field(
        proto.STRING,
        number=1,
    )
    data: bytes = proto.Field(
        proto.BYTES,
        number=2,
    )


class ModalityTokenCount(proto.Message):
    r"""Represents token counting info for a single modality.

    Attributes:
        modality (google.ai.generativelanguage_v1.types.Modality):
            The modality associated with this token
            count.
        token_count (int):
            Number of tokens.
    """

    modality: "Modality" = proto.Field(
        proto.ENUM,
        number=1,
        enum="Modality",
    )
    token_count: int = proto.Field(
        proto.INT32,
        number=2,
    )


__all__ = tuple(sorted(__protobuf__.manifest))
