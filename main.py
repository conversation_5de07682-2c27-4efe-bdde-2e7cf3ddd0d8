# -*- coding: utf-8 -*-
"""Untitled144.ipynb

Automatically generated by <PERSON><PERSON>.

Original file is located at
    https://colab.research.google.com/drive/1tZRnyQsQHmcw3YzDH1j_AUuO6UsSkcSs
"""

import os
import yaml
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Load prompts from YAML file
with open('prompts.yaml', 'r') as f:
    prompts = yaml.load(f, Loader=yaml.SafeLoader)
import yaml
import getpass
from typing import TypedDict, List, Dict, Any
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import create_react_agent
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_community.tools.tavily_search import TavilySearchResults
from langchain.tools import tool
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_experimental.tools import PythonREPLTool
import ast
import re
# Remove IPython dependencies for non-notebook environment
# from IPython.display import Image, display
# from langchain_core.runnables.graph import MermaidDrawMethod




# Environment setup - keys are now loaded from .env file

"""
Self-Documenting Code Analysis Agent System

This system uses multiple specialized agents to analyze, document, and validate Python code.
Each agent has a specific role and uses tailored prompts for optimal performance.
"""

import os
import yaml
import getpass
from typing import TypedDict, List, Dict, Any
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import create_react_agent
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_community.tools.tavily_search import TavilySearchResults
from langchain.tools import tool
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_experimental.tools import PythonREPLTool
import ast
import re
# Remove IPython dependencies for non-notebook environment

# Environment Setup
def setup_environment():
    """Set up API keys for the agents"""
    if "GOOGLE_API_KEY" not in os.environ:
        os.environ["GOOGLE_API_KEY"] = getpass.getpass("Enter your Google API Key: ")

    if "TAVILY_API_KEY" not in os.environ:
        os.environ["TAVILY_API_KEY"] = getpass.getpass("Enter your Tavily API Key: ")

# State Definition
class DocumentationState(TypedDict):
    """
    Shared state between all agents in the workflow

    Contains:
    - Code being processed
    - Analysis results
    - Documentation status
    - Generated outputs
    """
    original_code: str
    current_code: str
    documentation_quality: str
    libraries_used: List[str]
    issues_found: List[str]
    sample_runs: List[Dict[str, Any]]
    final_output: str
    current_step: str

# Tools Definition
@tool
def analyze_code_structure(code: str) -> str:
    """
    Analyze Python code to extract imports, functions, and classes

    Args:
        code: Python code as string

    Returns:
        Formatted analysis of code structure
    """
    tree = ast.parse(code)

    imports = []
    functions = []
    classes = []

    for node in ast.walk(tree):
        if isinstance(node, ast.Import):
            for alias in node.names:
                imports.append(alias.name)
        elif isinstance(node, ast.ImportFrom):
            module = node.module or ""
            for alias in node.names:
                imports.append(f"{module}.{alias.name}")
        elif isinstance(node, ast.FunctionDef):
            functions.append(node.name)
        elif isinstance(node, ast.ClassDef):
            classes.append(node.name)

    return f"Imports: {imports}, Functions: {functions}, Classes: {classes}"

@tool
def search_library_info(library_name: str) -> str:
    """
    Search for library documentation and usage examples

    Args:
        library_name: Name of the Python library

    Returns:
        Documentation information about the library
    """
    search_tool = TavilySearchResults(max_results=2)
    query = f"{library_name} python library documentation examples"
    results = search_tool.invoke(query)

    formatted_results = []
    for result in results:
        content = result.get('content', 'No content')[:200]
        formatted_results.append(f"Source: {result.get('url', 'N/A')}\nContent: {content}...")

    return "\n---\n".join(formatted_results)

# Agent Prompts loaded from YAML
ANALYZER_PROMPT = prompts['analyzer_prompt']
RESEARCHER_PROMPT = prompts['researcher_prompt']
DOCUMENTER_PROMPT = prompts['documenter_prompt']
VALIDATOR_PROMPT = prompts['validator_prompt']

# Initialize Models and Agents
def create_agents():
    """Create specialized agents for each task"""
    model = ChatGoogleGenerativeAI(
        model="gemini-2.5-flash",
        temperature=0.3,
        google_api_key=os.environ["GOOGLE_API_KEY"]
    )

    # Tools for each agent
    analysis_tools = [analyze_code_structure]
    research_tools = [search_library_info]

    # Create specialized agents with different prompts
    analyzer_agent = create_react_agent(
        model=model,
        tools=analysis_tools,
        prompt=ChatPromptTemplate.from_messages([
            ("system", ANALYZER_PROMPT),
            ("placeholder", "{messages}")
        ])
    )

    researcher_agent = create_react_agent(
        model=model,
        tools=research_tools,
        prompt=ChatPromptTemplate.from_messages([
            ("system", RESEARCHER_PROMPT),
            ("placeholder", "{messages}")
        ])
    )

    documenter_agent = create_react_agent(
        model=model,
        tools=[],
        prompt=ChatPromptTemplate.from_messages([
            ("system", DOCUMENTER_PROMPT),
            ("placeholder", "{messages}")
        ])
    )

    validator_agent = create_react_agent(
        model=model,
        tools=[],
        prompt=ChatPromptTemplate.from_messages([
            ("system", VALIDATOR_PROMPT),
            ("placeholder", "{messages}")
        ])
    )

    return analyzer_agent, researcher_agent, documenter_agent, validator_agent

# Workflow Nodes
def analysis_node(state: DocumentationState) -> DocumentationState:
    """
    Analyze code structure and determine documentation needs

    Uses the analyzer agent to examine code and extract key information
    """
    analyzer_agent, _, _, _ = create_agents()

    # Analyze the code
    analysis_input = {
        "messages": [HumanMessage(content=f"Analyze this Python code structure:\n\n{state['original_code']}")]
    }

    result = analyzer_agent.invoke(analysis_input)
    response_text = result["messages"][-1].content

    # Extract libraries (simple pattern matching)
    libraries = []
    for line in state['original_code'].split('\n'):
        if line.strip().startswith(('import ', 'from ')):
            if 'import' in line:
                lib = line.split('import')[1].strip().split()[0].split('.')[0]
                libraries.append(lib)

    # Check documentation quality
    has_docstrings = '"""' in state['original_code'] or "'''" in state['original_code']
    has_comments = '#' in state['original_code']

    if has_docstrings and has_comments:
        quality = "good"
    elif has_docstrings or has_comments:
        quality = "partial"
    else:
        quality = "missing"

    return {
        **state,
        "current_code": state['original_code'],
        "documentation_quality": quality,
        "libraries_used": libraries,
        "current_step": "analyzed"
    }

def research_node(state: DocumentationState) -> DocumentationState:
    """
    Research libraries used in the code for better context

    Uses the researcher agent to gather information about imported libraries
    """
    _, researcher_agent, _, _ = create_agents()

    # Research non-standard libraries
    standard_libs = ['os', 'sys', 're', 'json', 'math', 'datetime', 'collections']
    libraries_to_research = [lib for lib in state['libraries_used'] if lib not in standard_libs]

    research_info = ""
    for library in libraries_to_research[:3]:  # Limit to 3 libraries
        research_input = {
            "messages": [HumanMessage(content=f"Research the {library} library and provide key usage information")]
        }

        result = researcher_agent.invoke(research_input)
        research_info += f"\n{library}: {result['messages'][-1].content[:200]}...\n"

    return {
        **state,
        "current_step": "researched"
    }

def documentation_node(state: DocumentationState) -> DocumentationState:
    """
    Generate comprehensive documentation for the code

    Uses the documenter agent to add docstrings and comments
    """
    _, _, documenter_agent, _ = create_agents()

    doc_input = {
        "messages": [HumanMessage(content=f"""
        Add comprehensive documentation to this Python code:

        {state['current_code']}

        Libraries used: {', '.join(state['libraries_used'])}

        Add docstrings and comments. Return only the documented code.
        """)]
    }

    result = documenter_agent.invoke(doc_input)
    documented_code = result["messages"][-1].content

    # Clean up the response to extract just the code
    if "```python" in documented_code:
        documented_code = documented_code.split("```python")[1].split("```")[0].strip()
    elif "```" in documented_code:
        documented_code = documented_code.split("```")[1].split("```")[0].strip()

    return {
        **state,
        "current_code": documented_code,
        "current_step": "documented"
    }

def validation_node(state: DocumentationState) -> DocumentationState:
    """
    Validate code and generate test samples

    Uses the validator agent to find issues and create sample runs
    """
    _, _, _, validator_agent = create_agents()

    # Find issues
    issue_input = {
        "messages": [HumanMessage(content=f"""
        Analyze this code for potential issues:

        {state['current_code']}

        List any syntax errors, logic issues, or improvements needed.
        """)]
    }

    issue_result = validator_agent.invoke(issue_input)
    issues_text = issue_result["messages"][-1].content

    # Extract issues (simple parsing)
    issues = [line.strip() for line in issues_text.split('\n') if line.strip() and
              any(keyword in line.lower() for keyword in ['issue', 'error', 'problem', 'warning'])]

    # Generate sample runs
    sample_input = {
        "messages": [HumanMessage(content=f"""
        Create 3 practical sample test runs for this code. For each test, provide:
        1. The input values or data
        2. The expected output/result
        3. A brief description of what the test demonstrates

        Code to test:
        {state['current_code']}

        Format each test as:
        Test X: [description] → Input: [values] → Expected: [result]

        Make the tests realistic and cover different scenarios (normal case, edge case, etc.).
        """)]
    }

    sample_result = validator_agent.invoke(sample_input)
    sample_text = sample_result["messages"][-1].content

    # Parse sample runs with improved extraction
    sample_runs = []
    lines = sample_text.split('\n')
    for line in lines:
        line = line.strip()
        if ('Test' in line or 'test' in line) and ('→' in line or '->' in line or '|' in line):
            # Try different formats
            if '→' in line:
                parts = line.split('→')
            elif '->' in line:
                parts = line.split('->')
            else:
                parts = line.split('|')

            if len(parts) >= 2:
                test_desc = parts[0].strip()
                remaining = '→'.join(parts[1:]) if len(parts) > 2 else parts[1]

                # Extract input and expected
                if 'Input:' in remaining and 'Expected:' in remaining:
                    input_start = remaining.find('Input:') + 6
                    expected_start = remaining.find('Expected:')
                    if expected_start > input_start:
                        input_val = remaining[input_start:expected_start].strip()
                        expected_val = remaining[expected_start + 9:].strip()
                        sample_runs.append(f"{test_desc} → {input_val} → {expected_val}")
                else:
                    sample_runs.append(line)

    # If no tests found, create simple fallback
    if not sample_runs:
        sample_runs = [
            "Basic test with sample data",
            "Edge case test with boundary values"
        ]

    return {
        **state,
        "issues_found": issues,
        "sample_runs": sample_runs,
        "current_step": "validated"
    }

def output_node(state: DocumentationState) -> DocumentationState:
    """
    Generate final comprehensive output

    Combines all analysis results into a formatted report
    """
    # Create final output with all information
    output = f"""# Code Documentation Report

## Libraries Used
{', '.join(state['libraries_used'])}

## Documentation Quality
{state['documentation_quality']}

## Documented Code
```python
{state['current_code']}
```

## Issues Found
{chr(10).join([f"- {issue}" for issue in state['issues_found']]) if state['issues_found'] else "No issues found"}

## Sample Test Runs
{chr(10).join([f"**Test {i+1}:** {sample}"
               for i, sample in enumerate(state['sample_runs'])]) if state['sample_runs'] else "No sample runs generated"}

---
*Generated by LangGraph Documentation Agent System*
"""

    return {
        **state,
        "final_output": output,
        "current_step": "completed"
    }

# Workflow Creation
def create_documentation_workflow():
    """
    Create and configure the documentation workflow

    Returns:
        Compiled LangGraph workflow for code documentation
    """
    workflow = StateGraph(DocumentationState)

    # Add all nodes to the workflow
    workflow.add_node("analyze", analysis_node)
    workflow.add_node("research", research_node)
    workflow.add_node("document", documentation_node)
    workflow.add_node("validate", validation_node)
    workflow.add_node("output", output_node)

    # Define the flow: analyze → research → document → validate → output
    workflow.add_edge("analyze", "research")
    workflow.add_edge("research", "document")
    workflow.add_edge("document", "validate")
    workflow.add_edge("validate", "output")
    workflow.add_edge("output", END)

    # Set entry point
    workflow.set_entry_point("analyze")

    # Compile the workflow first
    compiled_workflow = workflow.compile()

    # Save workflow visualization as PNG
    try:
        # Try using Mermaid.ink API first (no additional packages required)
        graph_png = compiled_workflow.get_graph().draw_mermaid_png()
        with open("workflow_diagram.png", "wb") as f:
            f.write(graph_png)
        print("Workflow diagram saved as workflow_diagram.png")
    except Exception as e:
        print(f"Could not save workflow diagram with Mermaid.ink: {e}")
        # Try using graphviz as fallback
        try:
            graph_png = compiled_workflow.get_graph().draw_png()
            with open("workflow_diagram.png", "wb") as f:
                f.write(graph_png)
            print("Workflow diagram saved as workflow_diagram.png (using graphviz)")
        except Exception as e2:
            print(f"Graphviz method also failed: {e2}")
            # Save as Mermaid text file as final fallback
            try:
                mermaid_text = compiled_workflow.get_graph().draw_mermaid()
                with open("workflow_diagram.mmd", "w") as f:
                    f.write(mermaid_text)
                print("Workflow diagram saved as workflow_diagram.mmd (Mermaid text format)")
            except Exception as e3:
                print(f"All visualization methods failed: {e3}")

    return compiled_workflow

def display_workflow():
    """Display the workflow graph visualization"""
    workflow = create_documentation_workflow()
    try:
        # Try using Mermaid.ink API first
        graph_png = workflow.get_graph().draw_mermaid_png()
        with open("workflow_diagram.png", "wb") as f:
            f.write(graph_png)
        print("Workflow diagram saved as workflow_diagram.png")
    except Exception as e:
        print(f"Could not save workflow diagram with Mermaid.ink: {e}")
        # Try using graphviz as fallback
        try:
            graph_png = workflow.get_graph().draw_png()
            with open("workflow_diagram.png", "wb") as f:
                f.write(graph_png)
            print("Workflow diagram saved as workflow_diagram.png (using graphviz)")
        except Exception as e2:
            print(f"Graphviz method also failed: {e2}")
            # Save as Mermaid text file as final fallback
            try:
                mermaid_text = workflow.get_graph().draw_mermaid()
                with open("workflow_diagram.mmd", "w") as f:
                    f.write(mermaid_text)
                print("Workflow diagram saved as workflow_diagram.mmd (Mermaid text format)")
            except Exception as e3:
                print(f"All visualization methods failed: {e3}")

def run_documentation_agent(code_input: str) -> Dict:
    """
    Run the complete documentation workflow on provided code with streaming

    Args:
        code_input: Python code to analyze and document

    Returns:
        Dictionary containing all results from the workflow
    """
    # Set up environment
    setup_environment()

    # Create and run workflow
    app = create_documentation_workflow()

    # Initialize state
    initial_state = {
        "original_code": code_input,
        "current_code": code_input,
        "documentation_quality": "",
        "libraries_used": [],
        "issues_found": [],
        "sample_runs": [],
        "final_output": "",
        "current_step": "start"
    }

    print("🚀 Starting Documentation Workflow...")
    print("=" * 60)

    # Stream the workflow execution
    final_result = None
    for step in app.stream(initial_state):
        step_name = list(step.keys())[0]
        step_data = step[step_name]

        print(f"\n📍 Executing: {step_name.upper()}")
        print("-" * 40)

        if step_name == "analyze":
            print(f"🔍 Analyzing code structure...")
            if step_data.get("libraries_used"):
                print(f"📚 Found libraries: {', '.join(step_data['libraries_used'])}")

        elif step_name == "research":
            print(f"🔬 Researching libraries...")
            if step_data.get("libraries_used"):
                print(f"📖 Research completed for {len(step_data['libraries_used'])} libraries")

        elif step_name == "document":
            print(f"📝 Generating documentation...")
            if step_data.get("current_code") and len(step_data.get("current_code", "")) > len(code_input):
                print(f"✅ Documentation added to code")

        elif step_name == "validate":
            print(f"🔍 Validating code and identifying issues...")
            if step_data.get("issues_found"):
                print(f"⚠️  Found {len(step_data['issues_found'])} potential issues")
            if step_data.get("sample_runs"):
                print(f"🧪 Generated {len(step_data['sample_runs'])} sample tests")

        elif step_name == "output":
            print(f"📋 Generating final report...")
            print(f"✅ Workflow completed successfully!")
            final_result = step_data

    # Display results
    print("\n" + "=" * 60)
    print("DOCUMENTATION WORKFLOW COMPLETED")
    print("=" * 60)
    print(f"Final Step: {final_result['current_step']}")
    print(f"Libraries Found: {len(final_result['libraries_used'])}")
    print(f"Issues Identified: {len(final_result['issues_found'])}")
    print(f"Sample Tests: {len(final_result['sample_runs'])}")
    print("\n" + final_result['final_output'])

    # Save results to file
    save_results_to_file(final_result)

    return final_result

def save_results_to_file(result: Dict):
    """
    Save the workflow results to a markdown file

    Args:
        result: Dictionary containing workflow results
    """
    try:
        timestamp = __import__('datetime').datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"documentation_results_{timestamp}.md"

        with open(filename, 'w', encoding='utf-8') as f:
            f.write("# Code Documentation Results\n\n")
            f.write(f"**Generated on:** {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            f.write("## Libraries Found\n")
            if result['libraries_used']:
                for lib in result['libraries_used']:
                    f.write(f"- {lib}\n")
            else:
                f.write("- No libraries identified\n")
            f.write("\n")

            f.write("## Issues Identified\n")
            if result['issues_found']:
                for i, issue in enumerate(result['issues_found'], 1):
                    f.write(f"{i}. {issue}\n")
            else:
                f.write("- No issues identified\n")
            f.write("\n")

            f.write("## Sample Test Runs\n")
            if result['sample_runs']:
                for i, test in enumerate(result['sample_runs'], 1):
                    f.write(f"**Test {i}:** {test}\n")
            else:
                f.write("- No sample tests generated\n")
            f.write("\n")

            f.write("## How to Use\n")
            f.write("1. Review the documented code below\n")
            f.write("2. Address any issues identified above\n")
            f.write("3. Run the sample tests to verify functionality\n")
            f.write("4. Integrate the improved code into your project\n\n")

            f.write("## Full Documentation Report\n")
            f.write(result['final_output'])

        print(f"📄 Results saved to {filename}")

    except Exception as e:
        print(f"Error saving results to file: {e}")

def write_code_to_file(filename: str):
    """
    Write the current script to a file

    Args:
        filename: Name of the file to write to
    """
    try:
        # Read the current script file
        with open(__file__, 'r', encoding='utf-8') as f:
            current_code = f.read()

        # Write to the specified file
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(current_code)

        print(f"Current script saved to {filename}")
    except Exception as e:
        print(f"Error saving script to {filename}: {e}")

# Example Usage
if __name__ == "__main__":
    # Display workflow visualization
    print("Documentation Agent Workflow:")
    #display_workflow()

    # Three different sample codes to test thoroughly
    sample_codes = {
        "Data Analysis": """
import pandas as pd
import numpy as np

def process_data(data, threshold=0.5):
    filtered = data[data > threshold]
    return filtered.mean()

class DataAnalyzer:
    def __init__(self, data):
        self.data = data

    def analyze(self):
        result = process_data(self.data)
        return result * 2

data = np.array([0.1, 0.7, 0.9, 0.3, 0.8])
analyzer = DataAnalyzer(data)
print(analyzer.analyze())
""",

        "Web Scraping": """
import requests
from bs4 import BeautifulSoup
import json

def fetch_webpage(url):
    response = requests.get(url)
    return response.text

def parse_html(html_content):
    soup = BeautifulSoup(html_content, 'html.parser')
    titles = soup.find_all('h1')
    return [title.text for title in titles]

class WebScraper:
    def __init__(self, base_url):
        self.base_url = base_url
        self.data = []

    def scrape_page(self, endpoint):
        full_url = self.base_url + endpoint
        html = fetch_webpage(full_url)
        titles = parse_html(html)
        self.data.extend(titles)
        return len(titles)

scraper = WebScraper("https://example.com")
count = scraper.scrape_page("/news")
print(f"Found {count} titles")
""",

        "Math Utilities": """
import math

def calculate_area(shape, **kwargs):
    if shape == "circle":
        return math.pi * kwargs["radius"] ** 2
    elif shape == "rectangle":
        return kwargs["width"] * kwargs["height"]
    else:
        return 0

def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

class Calculator:
    def __init__(self):
        self.history = []

    def add(self, a, b):
        result = a + b
        self.history.append(f"{a} + {b} = {result}")
        return result

    def get_history(self):
        return self.history

calc = Calculator()
result = calc.add(5, 3)
area = calculate_area("circle", radius=5)
fib = fibonacci(6)
print(f"Results: {result}, {area:.2f}, {fib}")
"""
    }

    # Test each sample code
    for name, code in sample_codes.items():
        print(f"\n{'='*80}")
        print(f"TESTING: {name}")
        print(f"{'='*80}")

        # Run the documentation agent
        result = run_documentation_agent(code)

        print(f"\n✅ Completed testing: {name}")
        print(f"Libraries: {len(result['libraries_used'])}, Issues: {len(result['issues_found'])}, Tests: {len(result['sample_runs'])}")

    # Write current code to file
    write_code_to_file("code.py")

